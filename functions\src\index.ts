import * as functions from 'firebase-functions';
import * as admin from 'firebase-admin';
import cors from 'cors';
import { CoordinatorAgent } from './agents/coordinator';
import { MemoryManager } from './utils/memory-manager';
import { ProcessingRequest, ChatRequest } from './types';

// Initialize Firebase Admin
admin.initializeApp();

// <PERSON><PERSON>hai<PERSON> will be initialized on first use

// CORS configuration
const corsHandler = cors({ origin: true });

// Main processing function - triggered when a new session is created
export const processAudio = functions.https.onCall(async (data: ProcessingRequest, context) => {
  try {
    // Verify authentication
    if (!context.auth) {
      throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
    }

    const { sessionId, audioFileUrl, userId, autoMode = false } = data;

    // Validate input
    if (!sessionId || !audioFileUrl || !userId) {
      throw new functions.https.HttpsError('invalid-argument', 'Missing required parameters');
    }

    // Verify user owns the session
    if (context.auth.uid !== userId) {
      throw new functions.https.HttpsError('permission-denied', 'User can only process their own sessions');
    }

    // Initialize context
    const agentContext = MemoryManager.createInitialContext(sessionId, userId);
    MemoryManager.addMemory(sessionId, 'audioFileUrl', audioFileUrl);

    // Start the coordinator
    const coordinator = CoordinatorAgent.getInstance();
    const result = await coordinator.orchestrateWorkflow(sessionId, undefined, autoMode);

    // Update Firestore with the result
    const db = admin.firestore();
    await db
      .collection('sessions')
      .doc(sessionId)
      .update({
        status: result.success ? 'processing' : 'error',
        updatedAt: admin.firestore.FieldValue.serverTimestamp(),
        agentStatuses: agentContext.agentStatuses
      });

    return {
      success: result.success,
      sessionId,
      message: result.success ? 'Processing started' : result.error,
      agentStatus: result.status
    };

  } catch (error) {
    console.error('Error in processAudio:', error);
    throw new functions.https.HttpsError('internal', 'Processing failed');
  }
});

// Chat function - handles user messages and description editing
export const chatWithAgent = functions.https.onCall(async (data: ChatRequest, context) => {
  try {
    // Verify authentication
    if (!context.auth) {
      throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
    }

    const { sessionId, message, userId } = data;

    // Validate input
    if (!sessionId || !message || !userId) {
      throw new functions.https.HttpsError('invalid-argument', 'Missing required parameters');
    }

    // Verify user owns the session
    if (context.auth.uid !== userId) {
      throw new functions.https.HttpsError('permission-denied', 'User can only chat with their own sessions');
    }

    // Get session context
    const agentContext = MemoryManager.getContext(sessionId);
    if (!agentContext) {
      throw new functions.https.HttpsError('not-found', 'Session not found');
    }

    // Process the message with coordinator
    const coordinator = CoordinatorAgent.getInstance();
    const result = await coordinator.handleUserMessage(sessionId, message);

    // Update Firestore with chat history
    const db = admin.firestore();
    const sessionRef = db.collection('sessions').doc(sessionId);
    await sessionRef.update({
      chatHistory: admin.firestore.FieldValue.arrayUnion({
        id: Date.now().toString(),
        role: 'user',
        content: message,
        timestamp: admin.firestore.FieldValue.serverTimestamp()
      }),
      updatedAt: admin.firestore.FieldValue.serverTimestamp()
    });

    // Add AI response to chat history
    if (result.success && result.data) {
      await sessionRef.update({
        chatHistory: admin.firestore.FieldValue.arrayUnion({
          id: (Date.now() + 1).toString(),
          role: 'assistant',
          content: result.data.message || 'Processing your request...',
          timestamp: admin.firestore.FieldValue.serverTimestamp()
        })
      });
    }

    return {
      success: result.success,
      message: result.data?.message || 'Message processed',
      updatedDescription: result.updatedContext?.currentDescription,
      agentStatus: result.status
    };

  } catch (error) {
    console.error('Error in chatWithAgent:', error);
    throw new functions.https.HttpsError('internal', 'Chat processing failed');
  }
});

// Get session status
export const getSessionStatus = functions.https.onCall(async (data: { sessionId: string }, context) => {
  try {
    if (!context.auth) {
      throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
    }

    const { sessionId } = data;
    if (!sessionId) {
      throw new functions.https.HttpsError('invalid-argument', 'Session ID required');
    }

    // Get session from Firestore
    const sessionDoc = await admin.firestore()
      .collection('sessions')
      .doc(sessionId)
      .get();

    if (!sessionDoc.exists) {
      throw new functions.https.HttpsError('not-found', 'Session not found');
    }

    const sessionData = sessionDoc.data();
    
    // Verify user owns the session
    if (sessionData?.userId !== context.auth.uid) {
      throw new functions.https.HttpsError('permission-denied', 'Access denied');
    }

    // Get agent context if available
    const agentContext = MemoryManager.getContext(sessionId);
    const contextSummary = agentContext ? MemoryManager.getContextSummary(sessionId) : null;

    return {
      session: sessionData,
      agentContext: contextSummary,
      agentStatuses: agentContext?.agentStatuses || {}
    };

  } catch (error) {
    console.error('Error in getSessionStatus:', error);
    throw new functions.https.HttpsError('internal', 'Failed to get session status');
  }
});

// Continue auto mode processing
export const continueAutoMode = functions.https.onCall(async (data: { sessionId: string }, context) => {
  try {
    if (!context.auth) {
      throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
    }

    const { sessionId } = data;
    if (!sessionId) {
      throw new functions.https.HttpsError('invalid-argument', 'Session ID required');
    }

    // Verify session ownership
    const sessionDoc = await admin.firestore()
      .collection('sessions')
      .doc(sessionId)
      .get();

    if (!sessionDoc.exists) {
      throw new functions.https.HttpsError('not-found', 'Session not found');
    }

    const sessionData = sessionDoc.data();
    if (sessionData?.userId !== context.auth.uid) {
      throw new functions.https.HttpsError('permission-denied', 'Access denied');
    }

    // Continue auto mode processing
    const coordinator = CoordinatorAgent.getInstance();
    const result = await coordinator.continueAutoMode(sessionId);

    return {
      success: result.success,
      message: result.data?.message || 'Auto mode continued',
      agentStatus: result.status
    };

  } catch (error) {
    console.error('Error in continueAutoMode:', error);
    throw new functions.https.HttpsError('internal', 'Auto mode continuation failed');
  }
});

// Cleanup function - runs periodically to clean up old contexts
export const cleanupOldSessions = functions.pubsub
  .schedule('every 24 hours')
  .onRun(async (context) => {
    try {
      const cleaned = MemoryManager.cleanup(24); // Clean up contexts older than 24 hours
      console.log(`Cleaned up ${cleaned} old session contexts`);
      return null;
    } catch (error) {
      console.error('Error in cleanup:', error);
      return null;
    }
  });

// HTTP endpoint for health check
export const healthCheck = functions.https.onRequest((req, res) => {
  corsHandler(req, res, () => {
    res.json({
      status: 'healthy',
      timestamp: new Date().toISOString(),
      version: '1.0.0'
    });
  });
});
